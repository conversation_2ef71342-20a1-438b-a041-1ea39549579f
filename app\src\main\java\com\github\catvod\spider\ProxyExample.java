package com.github.catvod.spider;

import android.content.Context;

import com.github.catvod.crawler.SpiderDebug;
import com.github.catvod.net.OkHttp;

/**
 * 代理服务器使用示例
 * 展示如何在爬虫中使用JNI代理服务器
 */
public class ProxyExample {
    
    /**
     * 示例1：基本使用
     */
    public static void basicUsage(Context context) {
        // 获取代理管理器实例
        ProxyManager proxyManager = Init.getProxyManager();
        
        // 检查是否已初始化
        if (!proxyManager.isInitialized()) {
            // 如果未初始化，先初始化
            if (!proxyManager.initialize(context)) {
                SpiderDebug.log("代理管理器初始化失败");
                return;
            }
        }
        
        // 启动代理服务器
        if (proxyManager.startProxy()) {
            SpiderDebug.log("代理服务器启动成功");
            
            // 获取代理URL
            String proxyUrl = proxyManager.getProxyUrl();
            SpiderDebug.log("代理URL: " + proxyUrl);
            
            // 使用代理请求网页
            String targetUrl = "https://www.example.com";
            String proxiedUrl = proxyUrl + "/proxy?url=" + targetUrl;
            
            try {
                String response = OkHttp.string(proxiedUrl);
                SpiderDebug.log("通过代理获取到响应，长度: " + response.length());
            } catch (Exception e) {
                SpiderDebug.log("代理请求失败: " + e.getMessage());
            }
        } else {
            SpiderDebug.log("代理服务器启动失败");
        }
    }
    
    /**
     * 示例2：在爬虫中使用代理
     */
    public static String fetchWithProxy(String url) {
        ProxyManager proxyManager = Init.getProxyManager();

        // 检查代理服务器是否运行
        if (!proxyManager.isProxyRunning()) {
            SpiderDebug.log("代理服务器未运行，尝试启动...");
            if (!proxyManager.startProxy()) {
                SpiderDebug.log("代理服务器启动失败，使用直连");
                return OkHttp.string(url);
            }
        }

        try {
            // 使用JniLoader的新方法直接获取代理URL
            String proxiedUrl = JniLoader.getProxyUrl(url);
            if (proxiedUrl.isEmpty()) {
                SpiderDebug.log("获取代理URL失败，使用直连");
                return OkHttp.string(url);
            }

            // 通过代理请求
            String response = OkHttp.string(proxiedUrl);
            SpiderDebug.log("通过代理成功获取: " + url);
            return response;
        } catch (Exception e) {
            SpiderDebug.log("代理请求失败，尝试直连: " + e.getMessage());
            // 代理失败时回退到直连
            return OkHttp.string(url);
        }
    }

    /**
     * 示例3：使用带参数的代理
     */
    public static String fetchWithProxyParams(String url, int chunkSize, int threadCount) {
        ProxyManager proxyManager = Init.getProxyManager();

        // 检查代理服务器是否运行
        if (!proxyManager.isProxyRunning()) {
            SpiderDebug.log("代理服务器未运行，尝试启动...");
            if (!proxyManager.startProxy()) {
                SpiderDebug.log("代理服务器启动失败，使用直连");
                return OkHttp.string(url);
            }
        }

        try {
            // 使用带参数的代理URL
            String proxiedUrl = JniLoader.getProxyUrlWithParams(url, threadCount, chunkSize);
            if (proxiedUrl.isEmpty()) {
                SpiderDebug.log("获取带参数的代理URL失败，使用直连");
                return OkHttp.string(url);
            }

            // 通过代理请求
            String response = OkHttp.string(proxiedUrl);
            SpiderDebug.log("通过带参数代理成功获取: " + url + ", 线程数: " + threadCount + ", 分片大小: " + chunkSize + "KB");
            return response;
        } catch (Exception e) {
            SpiderDebug.log("带参数代理请求失败，尝试直连: " + e.getMessage());
            // 代理失败时回退到直连
            return OkHttp.string(url);
        }
    }
    
    /**
     * 示例3：带参数的代理请求
     */
    public static String fetchWithProxyParams(String url, int chunkSize, int maxThreads) {
        ProxyManager proxyManager = Init.getProxyManager();
        
        if (!proxyManager.isProxyRunning()) {
            SpiderDebug.log("代理服务器未运行");
            return null;
        }
        
        // 构建带参数的代理URL
        String proxyUrl = proxyManager.getProxyUrl();
        StringBuilder proxiedUrl = new StringBuilder(proxyUrl);
        proxiedUrl.append("/proxy?url=").append(url);
        
        if (chunkSize > 0) {
            proxiedUrl.append("&chunk_size=").append(chunkSize);
        }
        
        if (maxThreads > 0) {
            proxiedUrl.append("&max_threads=").append(maxThreads);
        }
        
        try {
            String response = OkHttp.string(proxiedUrl.toString());
            SpiderDebug.log("通过代理（带参数）成功获取: " + url);
            return response;
        } catch (Exception e) {
            SpiderDebug.log("代理请求（带参数）失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 示例4：检查代理服务器状态
     */
    public static void checkProxyStatus() {
        ProxyManager proxyManager = Init.getProxyManager();
        
        SpiderDebug.log("=== 代理服务器状态检查 ===");
        SpiderDebug.log("初始化状态: " + proxyManager.isInitialized());
        SpiderDebug.log("运行状态: " + proxyManager.isProxyRunning());
        SpiderDebug.log("当前端口: " + proxyManager.getCurrentPort());
        SpiderDebug.log("代理URL: " + proxyManager.getProxyUrl());
        SpiderDebug.log("JNI库状态: " + JniLoader.isLoaded());
        SpiderDebug.log("详细状态:\n" + proxyManager.getStatusInfo());
    }
    
    /**
     * 示例5：重启代理服务器到新端口
     */
    public static boolean restartProxyToNewPort(int newPort) {
        ProxyManager proxyManager = Init.getProxyManager();
        
        SpiderDebug.log("重启代理服务器到端口: " + newPort);
        
        boolean result = proxyManager.restartProxy(newPort);
        if (result) {
            SpiderDebug.log("代理服务器重启成功，新端口: " + newPort);
            SpiderDebug.log("新的代理URL: " + proxyManager.getProxyUrl());
        } else {
            SpiderDebug.log("代理服务器重启失败");
        }
        
        return result;
    }
    
    /**
     * 示例6：安全地停止代理服务器
     */
    public static void safeStopProxy() {
        ProxyManager proxyManager = Init.getProxyManager();
        
        if (proxyManager.isProxyRunning()) {
            SpiderDebug.log("停止代理服务器...");
            proxyManager.stopProxy();
            
            // 等待一段时间确保完全停止
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            // 验证是否已停止
            if (!proxyManager.isProxyRunning()) {
                SpiderDebug.log("代理服务器已成功停止");
            } else {
                SpiderDebug.log("代理服务器可能未完全停止");
            }
        } else {
            SpiderDebug.log("代理服务器未运行，无需停止");
        }
    }
    
    /**
     * 示例7：在应用退出时清理资源
     */
    public static void cleanup() {
        SpiderDebug.log("清理代理服务器资源...");
        safeStopProxy();
        SpiderDebug.log("代理服务器资源清理完成");
    }
}
