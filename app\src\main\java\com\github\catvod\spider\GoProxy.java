package com.github.catvod.spider;

import android.content.Context;
import android.util.Log;

import com.github.catvod.net.OkHttp;
import com.github.catvod.utils.DownloadUtils;
import com.github.catvod.utils.Notify;
import com.github.catvod.utils.Path;
import com.github.catvod.utils.Shell;
import com.github.catvod.utils.ScriptUtils;

import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

import okhttp3.Response;

public class GoProxy implements Runnable {

    private static final String SHELL = "runproxy.sh";
    private static final String PROXY = "video_proxy";
    private final Context context;
    private final String abi;
    private final JSONObject serverConfig;

    public GoProxy(Context context, String abi, JSONObject serverConfig) {
        this.context = context;
        this.abi = abi;
        this.serverConfig = serverConfig;

    }

    @Override
    public void run() {
        Log.d("GoProxy", "版本：" + abi);
        if (!serverConfig.has("server_url")) {
            return;
        }

        String goProxyAbi;
        if (abi.contains("x86_64")) {
            goProxyAbi = "video_proxy-linux-amd64";
        } else if (abi.contains("arm64-v8a")) {
            goProxyAbi = "video_proxy-android-arm64";
        } else if (abi.contains("armeabi-v7a")) {
            goProxyAbi = "video_proxy-android-armv7a";
        } else {
            Log.d("GoProxy", "未知版本");
            return;
        }

        File proxyDir = Path.cache("ptv.cache/goproxy.cache");
        File goProxyFile = new File(proxyDir, goProxyAbi);

        if (!goProxyFile.exists()) {
            String downloadUrl = serverConfig.optString("server_url") + "/video_proxy/" + goProxyAbi;

            if (DownloadUtils.downloadFile(downloadUrl, goProxyFile)) {
                Notify.show("已加载" + goProxyAbi);
            }
        }

        // 创建并执行启动脚本
        String[] params = {};
        ScriptUtils.createAndRunScript(proxyDir, goProxyFile, SHELL, goProxyFile.getName(), params);
//        ScriptUtils.run(goProxyFile, params);
    }
}
