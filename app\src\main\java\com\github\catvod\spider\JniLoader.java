package com.github.catvod.spider;

import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.github.catvod.crawler.SpiderDebug;
import com.github.catvod.utils.Notify;
import com.flycc.vodproxy.sdk.Sdk;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * JNI动态加载器
 * 用于从assets/libs/目录中动态加载libgojni.so文件并调用其中的方法
 */
public class JniLoader {
    
    private static final String TAG = "JniLoader";
    private static final String LIB_NAME = "libgojni.so";
    private static boolean isLibraryLoaded = false;
    
    // 注意：不再声明native方法，直接使用SDK类
    
    /**
     * 初始化SDK（替代原来的动态加载）
     * @param context 应用上下文
     * @return 是否初始化成功
     */
    public static synchronized boolean loadLibrary(Context context) {
        if (isLibraryLoaded) {
            SpiderDebug.log("SDK 已经初始化过了");
            return true;
        }

        try {
            // 直接使用SDK，不需要手动加载so文件
            // gomobile构建的AAR会自动处理so文件的加载
            isLibraryLoaded = true;
            SpiderDebug.log("SDK 初始化成功");
            return true;

        } catch (Exception e) {
            SpiderDebug.log("SDK 初始化失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    // 注意：使用SDK后不再需要手动处理so文件
    
    /**
     * 启动代理服务器
     * @param port 端口号
     * @return 启动结果码
     */
    public static int startProxy(int port) {
        if (!isLibraryLoaded) {
            SpiderDebug.log("SDK 未初始化，无法启动代理服务器");
            return -1;
        }

        try {
            // 调用SDK的StartProxyServer方法
            long result = Sdk.startProxyServer(port);
            int resultCode = (int) result;
            SpiderDebug.log("代理服务器启动结果: " + resultCode + ", 端口: " + port);
            return resultCode;
        } catch (Exception e) {
            SpiderDebug.log("启动代理服务器失败: " + e.getMessage());
            e.printStackTrace();
            return -1;
        }
    }
    
    /**
     * 停止代理服务器
     */
    public static void stopProxy() {
        if (!isLibraryLoaded) {
            SpiderDebug.log("SDK 未初始化，无法停止代理服务器");
            return;
        }

        try {
            // 调用SDK的StopProxyServer方法
            Sdk.stopProxyServer();
            SpiderDebug.log("代理服务器已停止");
        } catch (Exception e) {
            SpiderDebug.log("停止代理服务器失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查代理服务器是否运行
     */
    public static boolean isProxyRunning() {
        if (!isLibraryLoaded) {
            return false;
        }

        try {
            // 调用SDK的IsServerRunning方法
            return Sdk.isServerRunning();
        } catch (Exception e) {
            SpiderDebug.log("检查代理服务器状态失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前服务器端口
     */
    public static int getServerPort() {
        if (!isLibraryLoaded) {
            return 0;
        }

        try {
            return (int) Sdk.getServerPort();
        } catch (Exception e) {
            SpiderDebug.log("获取服务器端口失败: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 获取代理URL
     * @param originalUrl 原始URL
     * @return 代理URL
     */
    public static String getProxyUrl(String originalUrl) {
        if (!isLibraryLoaded) {
            return "";
        }

        try {
            return Sdk.getProxyURL(originalUrl);
        } catch (Exception e) {
            SpiderDebug.log("获取代理URL失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * 获取带参数的代理URL
     * @param originalUrl 原始URL
     * @param threadCount 线程数
     * @param chunkSize 分片大小(KB)
     * @return 代理URL
     */
    public static String getProxyUrlWithParams(String originalUrl, int threadCount, int chunkSize) {
        if (!isLibraryLoaded) {
            return "";
        }

        try {
            return Sdk.getProxyURLWithParams(originalUrl, threadCount, chunkSize);
        } catch (Exception e) {
            SpiderDebug.log("获取带参数的代理URL失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * 获取库加载状态
     */
    public static boolean isLoaded() {
        return isLibraryLoaded;
    }
}
